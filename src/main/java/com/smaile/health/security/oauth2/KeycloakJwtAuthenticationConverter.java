package com.smaile.health.security.oauth2;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * JWT Authentication Converter for Keycloak tokens.
 * <p>
 * This converter extracts authorities from Keycloak JWT tokens and converts them
 * to Spring Security authorities compatible with the existing SMAILE authentication system.
 * <p>
 * Keycloak Token Structure:
 * - realm_access.roles: Realm-level roles
 * - resource_access.{client-id}.roles: Client-specific roles
 * - preferred_username: Username
 * - email: User email
 * - sub: Subject (Keycloak user ID)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/27
 */
@Component
@Slf4j
public class KeycloakJwtAuthenticationConverter implements Converter<Jwt, AbstractAuthenticationToken> {

    private static final String REALM_ACCESS_CLAIM = "realm_access";
    private static final String RESOURCE_ACCESS_CLAIM = "resource_access";
    private static final String ROLES_CLAIM = "roles";
    private static final String PREFERRED_USERNAME_CLAIM = "preferred_username";
    private static final String EMAIL_CLAIM = "email";
    private static final String SUBJECT_CLAIM = "sub";
    private static final String ROLE_PREFIX = "ROLE_";

    @Override
    public AbstractAuthenticationToken convert(Jwt jwt) {
        log.debug("Converting JWT token for user: {}", jwt.getClaimAsString(EMAIL_CLAIM));

        try {
            Collection<GrantedAuthority> authorities = extractAuthorities(jwt);
            String principalName = extractPrincipalName(jwt);

            JwtAuthenticationToken authenticationToken = new JwtAuthenticationToken(jwt, authorities, principalName);

            log.debug("JWT conversion successful for user: {} with {} authorities",
                    principalName, authorities.size());

            return authenticationToken;

        } catch (Exception e) {
            log.error("Failed to convert JWT token: {}", e.getMessage(), e);
            // Return token with minimal authorities to allow basic authentication
            return new JwtAuthenticationToken(jwt, Collections.emptyList(), jwt.getSubject());
        }
    }

    /**
     * Extracts authorities from Keycloak JWT token.
     *
     * @param jwt The JWT token
     * @return Collection of granted authorities
     */
    private Collection<GrantedAuthority> extractAuthorities(Jwt jwt) {

        Collection<String> realmRoles = extractRealmRoles(jwt);
        Set<GrantedAuthority> authorities = realmRoles.stream()
                .map(role -> new SimpleGrantedAuthority(ROLE_PREFIX + role.toUpperCase()))
                .collect(Collectors.toSet());

        Collection<String> resourceRoles = extractResourceRoles(jwt);
        authorities.addAll(resourceRoles.stream()
                .map(role -> new SimpleGrantedAuthority(ROLE_PREFIX + role.toUpperCase()))
                .collect(Collectors.toSet()));

        authorities.addAll(extractOrganizationAuthorities(jwt));

        log.debug("Extracted {} authorities from JWT: {}", authorities.size(),
                authorities.stream()
                        .map(GrantedAuthority::getAuthority)
                        .toList());

        return authorities;
    }

    /**
     * Extracts realm-level roles from JWT token.
     */
    @SuppressWarnings("unchecked")
    private Collection<String> extractRealmRoles(Jwt jwt) {
        Map<String, Object> realmAccess = jwt.getClaimAsMap(REALM_ACCESS_CLAIM);
        if (realmAccess == null) {
            return Collections.emptyList();
        }

        Object roles = realmAccess.get(ROLES_CLAIM);
        if (roles instanceof Collection) {
            return ((Collection<?>) roles).stream()
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .filter(StringUtils::hasText)
                    .toList();
        }

        return Collections.emptyList();
    }

    /**
     * Extracts resource/client-specific roles from JWT token.
     */
    @SuppressWarnings("unchecked")
    private Collection<String> extractResourceRoles(Jwt jwt) {
        Map<String, Object> resourceAccess = jwt.getClaimAsMap(RESOURCE_ACCESS_CLAIM);
        if (resourceAccess == null) {
            return Collections.emptyList();
        }

        Set<String> allResourceRoles = new HashSet<>();

        // Iterate through all clients/resources
        for (Map.Entry<String, Object> entry : resourceAccess.entrySet()) {
            String clientId = entry.getKey();
            Object clientAccess = entry.getValue();

            if (clientAccess instanceof Map) {
                Map<String, Object> clientAccessMap = (Map<String, Object>) clientAccess;
                Object roles = clientAccessMap.get(ROLES_CLAIM);

                if (roles instanceof Collection) {
                    Collection<String> clientRoles = ((Collection<?>) roles).stream()
                            .filter(Objects::nonNull)
                            .map(Object::toString)
                            .filter(StringUtils::hasText)
                            .collect(Collectors.toList());

                    // Prefix with client ID for uniqueness
                    allResourceRoles.addAll(clientRoles.stream()
                            .map(role -> clientId + "_" + role)
                            .collect(Collectors.toList()));
                }
            }
        }

        return allResourceRoles;
    }

    /**
     * Extracts organization-specific authorities.
     * This is a placeholder for future integration with the existing user service.
     */
    private Collection<GrantedAuthority> extractOrganizationAuthorities(Jwt jwt) {
        // TODO: Integrate with existing UserService to get organization permissions
        // For now, return empty collection
        return Collections.emptyList();
    }

    /**
     * Extracts the principal name from JWT token.
     */
    private String extractPrincipalName(Jwt jwt) {
        String email = jwt.getClaimAsString(EMAIL_CLAIM);
        if (StringUtils.hasText(email)) {
            return email;
        }

        String preferredUsername = jwt.getClaimAsString(PREFERRED_USERNAME_CLAIM);
        if (StringUtils.hasText(preferredUsername)) {
            return preferredUsername;
        }

        return jwt.getSubject();
    }

}
