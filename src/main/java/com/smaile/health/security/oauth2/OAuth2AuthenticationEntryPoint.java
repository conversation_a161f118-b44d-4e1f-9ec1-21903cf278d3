package com.smaile.health.security.oauth2;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.model.response.BaseResponse;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.server.resource.BearerTokenError;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Custom OAuth2 Authentication Entry Point for handling authentication failures.
 * <p>
 * This entry point handles OAuth2 authentication failures and returns
 * structured error responses compatible with the existing SMAILE API format.
 * <p>
 * Error Types Handled:
 * - Invalid JWT tokens
 * - Expired tokens
 * - Missing Authorization header
 * - Malformed Bearer tokens
 * - Insufficient scope/authorities
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/27
 */
@Component("oauth2AuthenticationEntryPoint")
@RequiredArgsConstructor
@Slf4j
public class OAuth2AuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper objectMapper;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                        AuthenticationException authException) throws IOException, ServletException {
        
        log.warn("OAuth2 authentication failed for request {} {}: {}", 
                request.getMethod(), request.getRequestURI(), authException.getMessage());

        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");

        Map<String, Object> errorResponse = createErrorResponse(request, authException);
        
        response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
        response.getWriter().flush();
    }

    /**
     * Creates a structured error response for OAuth2 authentication failures.
     */
    private Map<String, Object> createErrorResponse(HttpServletRequest request, 
                                                   AuthenticationException authException) {
        Map<String, Object> errorResponse = new LinkedHashMap<>();
        
        // Basic error information
        errorResponse.put("timestamp", Instant.now().toString());
        errorResponse.put("status", HttpStatus.UNAUTHORIZED.value());
        errorResponse.put("error", "Unauthorized");
        errorResponse.put("path", request.getRequestURI());
        
        // OAuth2 specific error details
        if (authException instanceof OAuth2AuthenticationException oauth2Exception) {
            OAuth2Error oauth2Error = oauth2Exception.getError();
            errorResponse.put("error_code", oauth2Error.getErrorCode());
            errorResponse.put("error_description", oauth2Error.getDescription());
            
            // Add WWW-Authenticate header information for Bearer token errors
            if (oauth2Error instanceof BearerTokenError bearerTokenError) {
                errorResponse.put("error_uri", bearerTokenError.getUri());
                errorResponse.put("scope", bearerTokenError.getScope());
            }
        } else {
            errorResponse.put("error_code", "authentication_failed");
            errorResponse.put("error_description", authException.getMessage());
        }
        
        // Add helpful message for developers
        errorResponse.put("message", "Authentication failed. Please provide a valid Bearer token.");
        
        // Wrap in BaseResponse format for consistency
        BaseResponse<Object> baseResponse = BaseResponse.builder()
                .success(false)
                .message("Authentication failed")
                .data(errorResponse)
                .build();
        
        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", baseResponse.isSuccess());
        response.put("message", baseResponse.getMessage());
        response.put("data", baseResponse.getData());
        
        return response;
    }
}
