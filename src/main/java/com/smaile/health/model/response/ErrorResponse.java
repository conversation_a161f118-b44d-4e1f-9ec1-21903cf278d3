package com.smaile.health.model.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Standard error response model for SMAILE Health application.
 * Provides consistent error structure across all API endpoints.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorResponse {

    /**
     * HTTP status code
     */
    private int status;

    /**
     * Error code for programmatic handling
     */
    private String errorCode;

    /**
     * Human-readable error message
     */
    private String message;

    /**
     * Detailed error description
     */
    private String detail;

    /**
     * Request path where the error occurred
     */
    private String path;

    /**
     * Timestamp when the error occurred
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();

    /**
     * Additional error data (validation errors, field errors, etc.)
     */
    private Map<String, Object> data;

    /**
     * Creates a basic error response with status, code, and message
     */
    public static ErrorResponse of(int status, String errorCode, String message) {
        return ErrorResponse.builder()
                .status(status)
                .errorCode(errorCode)
                .message(message)
                .build();
    }

    /**
     * Creates an error response with additional detail
     */
    public static ErrorResponse of(int status, String errorCode, String message, String detail) {
        return ErrorResponse.builder()
                .status(status)
                .errorCode(errorCode)
                .message(message)
                .detail(detail)
                .build();
    }

    /**
     * Creates an error response with path information
     */
    public static ErrorResponse of(int status, String errorCode, String message, String detail, String path) {
        return ErrorResponse.builder()
                .status(status)
                .errorCode(errorCode)
                .message(message)
                .detail(detail)
                .path(path)
                .build();
    }

    /**
     * Creates an error response with additional data
     */
    public static ErrorResponse of(int status, String errorCode, String message, String detail, String path, Map<String, Object> data) {
        return ErrorResponse.builder()
                .status(status)
                .errorCode(errorCode)
                .message(message)
                .detail(detail)
                .path(path)
                .data(data)
                .build();
    }
}
