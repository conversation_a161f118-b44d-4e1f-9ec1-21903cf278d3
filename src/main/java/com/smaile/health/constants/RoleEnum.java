package com.smaile.health.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum RoleEnum {
    SUPER_SMAILE_ADMIN("Smaile system Admin"),
    PROFESSIONAL("Professional"),
    IC_ADMIN("Insurance Company Admin"),
    IC_TPA_ADMIN("Insurance Company TPA Admin"),
    SMAILE_TPA_ADMIN("Smaile TPA Admin"),
    SMAILE_MP_ADMIN("Smaile Medical Provider Admin"),
    IC_MP_ADMIN("Insurance Company Medical Provider Admin"),
    SMAILE_TPA_MP_ADMIN("Smaile TPA Medical Provider Admin"),
    IC_TPA_MP_ADMIN("Insurance Company TPA Medical Provider Admin");

    private String description;

    private static final Map<String, RoleEnum> mappings = new HashMap<>();

    static {
        for (RoleEnum role : RoleEnum.values()) {
            mappings.put(role.name(), role);
        }
    }

    public static RoleEnum resolve(String roleName) {
        return (roleName == null ? null : mappings.get(roleName));
    }

    public boolean matches(String roleName) {
        return (this == resolve(roleName));
    }
}
