package com.smaile.health.controller;

import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.SmaileAuthenticationException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Test controller to demonstrate exception handling.
 * This controller is only for testing purposes.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/27
 */
@RestController
@RequestMapping("/test/exceptions")
public class TestExceptionController {

    @GetMapping("/runtime-error")
    public String throwRuntimeError() {
        throw new SmaileRuntimeException("Test runtime error");
    }

    @GetMapping("/not-found")
    public String throwNotFound() {
        throw new NotFoundException("Test resource not found");
    }

    @GetMapping("/validation-error")
    public String throwValidationError() {
        throw new ValidationException("Test validation error");
    }

    @GetMapping("/bad-credentials")
    public String throwBadCredentials() {
        throw new BadCredentialsException("Test bad credentials");
    }

    @GetMapping("/account-disabled")
    public String throwAccountDisabled() {
        throw new DisabledException("Test account disabled");
    }

    @GetMapping("/insufficient-authentication")
    public String throwInsufficientAuthentication() {
        throw new InsufficientAuthenticationException("Test insufficient authentication");
    }

    @GetMapping("/smaile-authentication-error")
    public String throwSmaileAuthenticationError() {
        throw new SmaileAuthenticationException("Test SMAILE authentication error");
    }

    @GetMapping("/generic-authentication-error")
    public String throwGenericAuthenticationError() {
        throw new AuthenticationException("Test generic authentication error") {};
    }

    @GetMapping("/access-denied")
    public String throwAccessDenied() {
        throw new AccessDeniedException("Test access denied");
    }

    @GetMapping("/generic-error")
    public String throwGenericError() {
        throw new RuntimeException("Test generic error");
    }
}
