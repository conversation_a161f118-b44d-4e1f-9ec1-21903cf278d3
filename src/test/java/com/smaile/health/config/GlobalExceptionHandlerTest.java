package com.smaile.health.config;

import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.SmaileAuthenticationException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.model.response.ErrorResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.AuthenticationException;

import jakarta.servlet.http.HttpServletRequest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * Unit tests for GlobalExceptionHandler.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/27
 */
@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {

    @InjectMocks
    private GlobalExceptionHandler globalExceptionHandler;

    @Mock
    private HttpServletRequest request;

    @BeforeEach
    void setUp() {
        when(request.getRequestURI()).thenReturn("/api/test");
    }

    // ========================================
    // BUSINESS LOGIC EXCEPTION TESTS
    // ========================================

    @Test
    void handleSmaileRuntimeException_ShouldReturn400() {
        // Given
        SmaileRuntimeException exception = new SmaileRuntimeException("Runtime error occurred");

        // When
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleSmaileRuntimeException(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(400);
        assertThat(response.getBody().getErrorCode()).isEqualTo("RUNTIME_ERROR");
        assertThat(response.getBody().getMessage()).isEqualTo("Runtime Error");
        assertThat(response.getBody().getDetail()).isEqualTo("Runtime error occurred");
        assertThat(response.getBody().getPath()).isEqualTo("/api/test");
    }

    @Test
    void handleNotFoundException_ShouldReturn404() {
        // Given
        NotFoundException exception = new NotFoundException("Resource not found");

        // When
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleNotFoundException(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(404);
        assertThat(response.getBody().getErrorCode()).isEqualTo("RESOURCE_NOT_FOUND");
        assertThat(response.getBody().getMessage()).isEqualTo("Resource Not Found");
        assertThat(response.getBody().getDetail()).isEqualTo("Resource not found");
    }

    @Test
    void handleValidationException_ShouldReturn400() {
        // Given
        ValidationException exception = new ValidationException("Validation failed");

        // When
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleValidationException(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(400);
        assertThat(response.getBody().getErrorCode()).isEqualTo("VALIDATION_FAILED");
        assertThat(response.getBody().getMessage()).isEqualTo("Validation Failed");
        assertThat(response.getBody().getDetail()).isEqualTo("Validation failed");
    }

    // ========================================
    // AUTHENTICATION EXCEPTION TESTS (HTTP 401)
    // ========================================

    @Test
    void handleBadCredentialsException_ShouldReturn401() {
        // Given
        BadCredentialsException exception = new BadCredentialsException("Invalid credentials");

        // When
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleBadCredentialsException(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(401);
        assertThat(response.getBody().getErrorCode()).isEqualTo("INVALID_CREDENTIALS");
        assertThat(response.getBody().getMessage()).isEqualTo("Authentication Failed");
        assertThat(response.getBody().getDetail()).isEqualTo("Invalid credentials provided");
    }

    @Test
    void handleDisabledException_ShouldReturn401() {
        // Given
        DisabledException exception = new DisabledException("Account disabled");

        // When
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleDisabledException(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(401);
        assertThat(response.getBody().getErrorCode()).isEqualTo("ACCOUNT_DISABLED");
        assertThat(response.getBody().getMessage()).isEqualTo("Account Disabled");
        assertThat(response.getBody().getDetail()).isEqualTo("User account is disabled");
    }

    @Test
    void handleInsufficientAuthenticationException_ShouldReturn401() {
        // Given
        InsufficientAuthenticationException exception = new InsufficientAuthenticationException("Insufficient authentication");

        // When
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleInsufficientAuthenticationException(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(401);
        assertThat(response.getBody().getErrorCode()).isEqualTo("INSUFFICIENT_AUTHENTICATION");
        assertThat(response.getBody().getMessage()).isEqualTo("Insufficient Authentication");
        assertThat(response.getBody().getDetail()).isEqualTo("Insufficient authentication for the requested operation");
    }

    @Test
    void handleSmaileAuthenticationException_ShouldReturn401() {
        // Given
        SmaileAuthenticationException exception = new SmaileAuthenticationException("SMAILE authentication failed");

        // When
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleSmaileAuthenticationException(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(401);
        assertThat(response.getBody().getErrorCode()).isEqualTo("SMAILE_AUTHENTICATION_ERROR");
        assertThat(response.getBody().getMessage()).isEqualTo("SMAILE Authentication Error");
        assertThat(response.getBody().getDetail()).isEqualTo("SMAILE authentication failed");
    }

    @Test
    void handleGenericAuthenticationException_ShouldReturn401() {
        // Given
        AuthenticationException exception = new AuthenticationException("Generic auth error") {};

        // When
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleAuthenticationException(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.UNAUTHORIZED);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(401);
        assertThat(response.getBody().getErrorCode()).isEqualTo("AUTHENTICATION_FAILED");
        assertThat(response.getBody().getMessage()).isEqualTo("Authentication Failed");
        assertThat(response.getBody().getDetail()).isEqualTo("Authentication failed");
    }

    // ========================================
    // AUTHORIZATION EXCEPTION TESTS (HTTP 403)
    // ========================================

    @Test
    void handleAccessDeniedException_ShouldReturn403() {
        // Given
        AccessDeniedException exception = new AccessDeniedException("Access denied");

        // When
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleAccessDeniedException(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.FORBIDDEN);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(403);
        assertThat(response.getBody().getErrorCode()).isEqualTo("ACCESS_DENIED");
        assertThat(response.getBody().getMessage()).isEqualTo("Access Denied");
        assertThat(response.getBody().getDetail()).isEqualTo("Insufficient permissions for the requested operation");
    }

    // ========================================
    // GENERIC EXCEPTION TESTS
    // ========================================

    @Test
    void handleGenericException_ShouldReturn500() {
        // Given
        Exception exception = new RuntimeException("Unexpected error");

        // When
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleGenericException(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(500);
        assertThat(response.getBody().getErrorCode()).isEqualTo("INTERNAL_SERVER_ERROR");
        assertThat(response.getBody().getMessage()).isEqualTo("Internal Server Error");
        assertThat(response.getBody().getDetail()).isEqualTo("An unexpected error occurred. Please contact support");
    }
}
